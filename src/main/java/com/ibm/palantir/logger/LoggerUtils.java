/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.logger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoggerUtils {
    private static final Logger LOG = LoggerFactory.getLogger(LoggerUtils.class);

    private static volatile LoggerUtils INSTANCE;

    public enum LogLevel {
        DEBUG, INFO, TRACE, WARN, ERROR
    }

    private LoggerUtils() {
    }

    public static void init() {
        if (INSTANCE == null) {
            synchronized (LoggerUtils.class) {
                if (INSTANCE == null) {
                    INSTANCE = new LoggerUtils();
                }
            }
        }
    }

    public static LoggerUtils getInstance() {
        if (INSTANCE == null)
            init();
        return INSTANCE;
    }

    private String format(String repo, String className, String methodName, String message) {
        return String.format("[%s|%s|%s] - %s", repo, className, methodName, message);
    }

    public void log(LogLevel level, String repoName, String className, String methodName, String message,
            Object... params) {
        String formatted = format(repoName, className, methodName, message);
        switch (level) {
            case DEBUG:
                LOG.debug(formatted, params);
                break;
            case INFO:
                LOG.info(formatted, params);
                break;
            case TRACE:
                LOG.trace(formatted, params);
                break;
            case WARN:
                LOG.warn(formatted, params);
                break;
            case ERROR:
                LOG.error(formatted, params);
                break;
            default:
                LOG.error("Invalid log mode!");
        }
    }

}