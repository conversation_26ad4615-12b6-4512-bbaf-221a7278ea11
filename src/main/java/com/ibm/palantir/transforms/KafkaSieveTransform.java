/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.transforms;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.ConnectRecord;
import org.apache.kafka.connect.data.Field;
import org.apache.kafka.connect.data.Schema;
import org.apache.kafka.connect.data.SchemaBuilder;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.transforms.Transformation;
import org.apache.kafka.connect.transforms.util.SimpleConfig;

import com.ibm.palantir.logger.LoggerUtils;
import com.ibm.palantir.logger.LoggerUtils.LogLevel;

public class KafkaSieveTransform<R extends ConnectRecord<R>> implements Transformation<R> {
    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = KafkaSieveTransform.class.getSimpleName();
    private static final String REPO_NAME = "Kafka-Sieve";

    private static final String IGNORED_COLUMNS_CONFIG = "ignore.columns";

    private static final ConfigDef CONFIG_DEF = new ConfigDef()
            .define(IGNORED_COLUMNS_CONFIG, ConfigDef.Type.STRING, "", ConfigDef.Importance.HIGH,
                    "Comma-separated list of columns to ignore for change detection");

    private Set<String> ignoredColumns;

    @Override
    public void configure(Map<String, ?> configs) {
        SimpleConfig config = new SimpleConfig(CONFIG_DEF, configs);
        String ignoreColumnsStr = config.getString(IGNORED_COLUMNS_CONFIG);

        Set<String> configuredIgnoredColumns = ignoreColumnsStr.isEmpty()
                ? Set.of()
                : Set.of(ignoreColumnsStr.split("\\s*,\\s*"));

        // Always ignore these
        Set<String> allIgnoredColumns = new HashSet<>(configuredIgnoredColumns);
        allIgnoredColumns.add("scan_date");

        this.ignoredColumns = Set.copyOf(allIgnoredColumns);
    }

    @Override
    public R apply(R record) {
        // Not a struct, return same record
        Object value = record.value();
        if (!(value instanceof Struct struct)) {
            return record; 
        }

        Struct before = struct.getStruct("before");
        Struct after = struct.getStruct("after");

        // Not a CDC update event, return same record
        if (before == null || after == null) {
            return record; 
        }

        //Record is struct type and is a CDC event
        Schema afterSchema = after.schema();
        boolean hasRelevantChange = false;

        for (Field field : afterSchema.fields()) {
            String name = field.name();
            if (ignoredColumns.contains(name))
                continue;

            Object beforeVal = before.get(name);
            Object afterVal = after.get(name);

            if (!Objects.equals(beforeVal, afterVal)) {
                hasRelevantChange = true;
                break;
            }
        }

        // Only ignored fields changed, drop the event
        if (!hasRelevantChange) {
            return null; 
        }

        // Build new schema with all fields from 'after', including unchanged fields
        SchemaBuilder schemaBuilder = SchemaBuilder.struct();
        for (Field field : afterSchema.fields()) {
            schemaBuilder.field(field.name(), field.schema());
        }
        Schema outputSchema = schemaBuilder.build();

        // Build new struct with all non-null and non-empty string values from 'after'
        Struct outputStruct = new Struct(outputSchema);
        for (Field field : afterSchema.fields()) {
            String fieldName = field.name();
            Object val = after.get(fieldName);
            boolean include = val != null && (!(val instanceof String) || !((String) val).isBlank());
            if (include) {
                outputStruct.put(fieldName, val);
                LOG.log(LogLevel.INFO, REPO_NAME, CLASSNAME, "apply", "Including field: {} with value {}", field.name(), val);
            } else {
                LOG.log(LogLevel.INFO, REPO_NAME, CLASSNAME, "apply", "Excluding field: {} with value {}", field.name(), val);
            }
        }

        return record.newRecord(
                record.topic(),
                record.kafkaPartition(),
                record.keySchema(),
                record.key(),
                outputSchema,
                outputStruct,
                record.timestamp());
    }

    @Override
    public ConfigDef config() {
        return CONFIG_DEF;
    }

    @Override
    public void close() {
        // No resources to close
    }
}
